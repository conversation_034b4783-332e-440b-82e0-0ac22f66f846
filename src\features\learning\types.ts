import { Resource } from "../resources/types";

export type ResourceCardProps = {
	id: number;
	title: string;
	description: string;
	thumbnail: string;
	type: "video" | "files" | "link";
	subject: "physics" | "mathematics" | "chemistry" | "english";
	resourceUrl?: string;
	fileType?: string;
	fileSize?: string;
};

export type Subject = {
	id: string;
	label: string;
};

export type ResourceType = {
	id: string;
	label: string;
};

export type SectionProps = {
	selectedSubject: string;
	setResourceType?: (type: string) => void;
	resources: Resource[];
};
