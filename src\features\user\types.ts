import { APIResponse } from "@/lib/api/types";

type Analytics = {
	mcqsSolvedCount: number;
	totalQuizzesTaken: number;
	avgTimePerQuiz: number;
	avgScorePerQuiz: number;
};

export type AnalyticsData = {
	mcqsSolvedCount: number;
	totalQuizzesTaken: number;
	avgTimePerQuiz: number;
	avgScorePerQuiz: number;
	correctAnswers?: number;
	wrongAnswers?: number;
	subjectWiseStats?: {
		subject: string;
		attempted: number;
		correct: number;
		wrong: number;
		accuracy: number;
	}[];
	difficultyStats?: {
		difficulty: string;
		attempted: number;
		correct: number;
		wrong: number;
		accuracy: number;
	}[];
	mcqTypeStats?: {
		type: string;
		attempted: number;
		correct: number;
		wrong: number;
		accuracy: number;
	}[];
	recentPerformance?: {
		date: string;
		score: number;
		attempted: number;
	}[];
};

export type DetailedAnalytics = {
	analytics: AnalyticsData;
};

export type User = {
	_id: string;
	firebase_id: string;
	name: string;
	email: string;
	phoneNumber: string;
	city: string;
	institute: string;
	role: string;
	educationBackground: string;
	currentClass: string;
	subjectGroup: any[];
	targetEntryTests: any[];
	analytics: Analytics;
	quizzes: any[];
};

export type ProfilePictureUploadUrl = {
	url: string;
	method: string;
	expiresIn: number;
};

export type ProfilePictureUrl = {
	url: string;
	expiresIn: number;
};

export type GetUserRes = APIResponse<User>;
export type OnboardUserRes = APIResponse<User>;
export type UpdateUserRes = APIResponse<User>;
export type ProfilePictureUploadUrlRes = APIResponse<ProfilePictureUploadUrl>;
export type ProfilePictureUrlRes = APIResponse<ProfilePictureUrl>;
export type GetAnalyticsRes = APIResponse<DetailedAnalytics>;
