import { api } from "@/lib/api";
import type { GetResourcesRes } from "./types";

export const getResources = (
	subject: string,
	resourceType: string,
	searchTerm?: string,
	page: number = 1,
	limit: number = 10
) => {
	// Create params object with pagination
	const params: Record<string, string | number> = {
		page,
		limit,
	};

	// Add search parameter if provided
	if (searchTerm && searchTerm.trim()) {
		// Replace spaces with + to match backend URL encoding expectations
		params["search"] = searchTerm.trim().replace(/\s+/g, "+");
	}

	return api.get<GetResourcesRes>(`resources/get/${subject}/${resourceType}`, {
		params,
	});
};
