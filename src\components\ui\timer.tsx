import { useState, useEffect, useCallback } from "react";
import { Clock } from "react-feather";
import { cn } from "@/lib/utils";

export interface TimerProps {
	initialMinutes: number;
	initialSeconds: number;
	className?: string;
	onTimeEnd?: () => void;
	showIcon?: boolean;
}

export function Timer({
	initialMinutes,
	initialSeconds,
	className,
	onTimeEnd,
	showIcon = true,
}: TimerProps) {
	// Convert initial minutes to hours and remaining minutes
	const initialHours = Math.floor(initialMinutes / 60);
	const initialRemainingMinutes = initialMinutes % 60;

	const [hours, setHours] = useState<number>(initialHours);
	const [minutes, setMinutes] = useState<number>(initialRemainingMinutes);
	const [seconds, setSeconds] = useState<number>(initialSeconds);
	const [isWarning, setIsWarning] = useState<boolean>(false);
	const [isDanger, setIsDanger] = useState<boolean>(false);
	const [hasFinished,setHasfinished] = useState<boolean>(false);

	const formatTime = useCallback((value: number) => {
		return value.toString().padStart(2, "0");
	}, []);

	useEffect(() => {
		if(hasFinished) return
		const totalTimeInSeconds =
			initialHours * 3600 + initialRemainingMinutes * 60 + initialSeconds;
		const warningThreshold = Math.max(totalTimeInSeconds * 0.25, 60); // 25% of total time or at least 1 minute
		const dangerThreshold = Math.max(totalTimeInSeconds * 0.1, 30); // 10% of total time or at least 30 seconds

		const timer = setInterval(() => {
			if (seconds > 0) {
				setSeconds(seconds - 1);
			} else if (minutes > 0) {
				setMinutes(minutes - 1);
				setSeconds(59);
			} else if (hours > 0) {
				setHours(hours - 1);
				setMinutes(59);
				setSeconds(59);
			} else {
				// Time is up
				clearInterval(timer);
				if (onTimeEnd) {
					setHasfinished(true)
					onTimeEnd();
				}
			}

			// Check if we should show warning (25% time left)
			const currentTotalSeconds = hours * 3600 + minutes * 60 + seconds;
			if (currentTotalSeconds <= warningThreshold && !isWarning) {
				setIsWarning(true);
			}
			// Check if we should show danger (10% time left)
			if (currentTotalSeconds <= dangerThreshold && !isDanger) {
				setIsDanger(true);
			}
		}, 1000);

		return () => clearInterval(timer);
	}, [
		hours,
		minutes,
		seconds,
		initialHours,
		initialRemainingMinutes,
		initialSeconds,
		isWarning,
		isDanger,
		onTimeEnd,
	]);

	return (
		<div
			className={cn(
				"flex items-center gap-2",
				isDanger
					? "text-red-600 animate-pulse"
					: isWarning
						? "text-amber-500"
						: "",
				className
			)}
		>
			{showIcon && <Clock className="w-4 h-4" />}
			<span>
				{formatTime(hours)}:{formatTime(minutes)}:{formatTime(seconds)}
			</span>
		</div>
	);
}
