import { Button } from "@/components/ui/button";
import { ChevronRight, HelpCircle } from "react-feather";
import { getContactUrl } from "@/lib/utils";

const NeedHelp = () => {
	const handleContactClick = () => {
		window.open(getContactUrl(), "_blank");
	};

	return (
		<Button
			variant={"inactive"}
			className="flex w-full justify-start px-0 py-2 h-24"
			onClick={handleContactClick}
		>
			<div className="p-4 bg-[#51A3A3]/20 rounded-[45%]">
				<HelpCircle size={24} className="text-tertiary" />
			</div>
			<div className="flex flex-col items-start">
				<p>Need help?</p>
				<p>Contact us</p>
			</div>
			<ChevronRight size={24} className="ml-auto" />
		</Button>
	);
};

export default NeedHelp;
