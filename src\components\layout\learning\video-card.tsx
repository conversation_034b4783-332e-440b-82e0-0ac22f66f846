import { Card, CardContent } from "@/components/ui/card";
import { Resource } from "@/features/resources/types";
import YouTube, { YouTubeProps } from "react-youtube";

export const VideoCard = ({ resource }: { resource: Resource }) => {
	const opts: YouTubeProps["opts"] = {
		width: "100%",
		height: "100%",
		playerVars: {
			autoplay: 0,
		},
	};
	return (
		<Card className="overflow-hidden flex gap-y-4 p-4 flex-col justify-between  min-w-64">
			<div className="relative h-64 w-full ">
				<YouTube
					className="h-full"
					videoId={resource.url.split("?v=")[1]}
					opts={opts}
				/>
			</div>
			<CardContent className="space-y-1 p-0">
				<h3 className="text-base">{resource.title}</h3>
				<p className="text-xs md:text-sm font-normal text-gray-600">
					{resource.description}
				</p>
			</CardContent>
		</Card>
	);
};
