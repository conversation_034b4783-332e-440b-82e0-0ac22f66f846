import * as React from "react";

import { cn } from "@/lib/utils";

export interface InputProps
	extends React.InputHTMLAttributes<HTMLInputElement> {
	startIcon?: any;
	endIcon?: any;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
	({ className, type, startIcon, endIcon, ...props }, ref) => {
		const StartIcon = startIcon;
		const EndIcon = endIcon;
		return (
			<div className="w-full relative">
				{StartIcon && (
					<div className="absolute left-3 top-1/2 transform -translate-y-1/2">
						<StartIcon className="size-6 text-gray-400" />
					</div>
				)}
				<input
					type={type}
					className={cn(
						"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm sm:text-base  file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
						startIcon ? "pl-12" : "",
						endIcon ? "pr-8" : "",
						className
					)}
					ref={ref}
					{...props}
				/>
				{EndIcon && (
					<div className="absolute right-3 top-1/2 transform -translate-y-1/2">
						<EndIcon className="text-muted-foreground" size={20} />
					</div>
				)}
			</div>
		);
	}
);
Input.displayName = "Input";

export { Input };
