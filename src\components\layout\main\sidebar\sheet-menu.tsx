import {
	Sheet,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { ICONS } from "@/lib/assets/images";
import { Menu as MenuIcon } from "react-feather";
import { UserNav } from "./user-nav";
import { MobileMenu } from "./mobile-menu";
import UpgradePlan from "../upgrade-plan";

export function SheetMenu() {
	return (
		<Sheet>
			<SheetTrigger className="lg:hidden" asChild>
				<MenuIcon size={24} />
			</SheetTrigger>
			<SheetContent
				className=" gap-y-3 w-4/5 pt-8 rounded-r-xl bg-primary px-3 h-full flex flex-col justify-between"
				side="left"
			>
				<SheetTitle className="hidden">Menu</SheetTitle>
				<SheetHeader className="text-left">
					{/* <Button
            className="flex items-start justify-start pb-2 pt-1"
            variant="link"
            asChild
          > */}
					<div className="pb-2">
						<img src={ICONS.expandedwhite} alt="Logo" className="h-10 w-1/2" />
					</div>
					{/* </Button> */}
				</SheetHeader>
				<UserNav />
				<MobileMenu isOpen />
				<UpgradePlan />
			</SheetContent>
		</Sheet>
	);
}
