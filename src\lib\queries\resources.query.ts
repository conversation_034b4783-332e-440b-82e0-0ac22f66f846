import { getResources } from "@/features/resources/services";
import { useQuery } from "@tanstack/react-query";

export const useGetResources = (
	subject: string,
	resourceType: string,
	searchTerms: string[] | string
) =>
	useQuery({
		queryKey: ["resources", subject, resourceType, searchTerms],
		queryFn: () => {
			// Convert searchTerms array to a space-separated string if it's an array
			const searchString = Array.isArray(searchTerms)
				? searchTerms.join(" ")
				: searchTerms;

			return getResources(subject, resourceType, searchString);
		},
		enabled: !!subject && !!resourceType,
		select: (data) => data.data,
	});
