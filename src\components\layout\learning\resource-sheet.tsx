import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { VideoCard } from "@/components/layout/learning/video-card";
import { FileCard } from "@/components/layout/learning/file-card";
import { LinkCard } from "@/components/layout/learning/link-card";
import { Button } from "@/components/ui/button";
import { Resource } from "@/features/resources/types";

type SheetButtonProps = {
	resourceType: "video" | "files" | "link";
	selectedSubject: string;
	setResourceType?: (type: string) => void;
	resources: Resource[];
};

export function SheetButton({
	resourceType,
	selectedSubject,
	resources,
	setResourceType,
}: SheetButtonProps) {
	const handleSheetOpen = (open: boolean) => {
		if (open) {
			setResourceType?.(resourceType);
		} else {
			setResourceType?.("");
		}
	};

	const renderContent = (resource: Resource) => {
		switch (resourceType) {
			case "video":
				return (
					<div className="w-full lg:w-1/2 p-2" key={resource.id}>
						<VideoCard resource={resource} />
					</div>
				);
			case "files":
				return <FileCard key={resource.id} resource={resource} />;
			case "link":
				return <LinkCard key={resource.id} resource={resource} />;
			default:
				return null;
		}
	};

	const resourceCount = resources.length;
	const resourceTypeLabel =
		resourceType === "video"
			? "Videos"
			: resourceType === "files"
				? "Files"
				: resourceType === "link"
					? "Links"
					: "";

	return (
		<Sheet onOpenChange={handleSheetOpen}>
			<SheetTrigger className="lg:hidden" asChild>
				<Button className="text-sm text-gray-500" variant="link">
					see all
				</Button>
			</SheetTrigger>
			<SheetContent
				className="gap-y-3 w-full pt-8 rounded-t-xl bg-background px-3 h-[92%] flex flex-col"
				side="bottom"
				hideOverlay={true}
			>
				<SheetHeader className="text-left space-y-2">
					<SheetTitle className="text-base font-medium text-gray-400">
						{resourceCount}{" "}
						{resourceCount === 1
							? resourceTypeLabel.slice(0, -1)
							: resourceTypeLabel.toLowerCase()}{" "}
						for "{selectedSubject}"
					</SheetTitle>
				</SheetHeader>
				<div
					className={`flex-1 overflow-y-auto mt-4 ${
						resourceType === "video"
							? "sm:grid sm:grid-cols-2 flex flex-col pace-y-2"
							: "space-y-2"
					}`}
				>
					{resources.map(renderContent)}
				</div>
			</SheetContent>
		</Sheet>
	);
}
