import {
	Card,
	CardContent,
	CardTitle,
	CardDescription,
} from "@/components/ui/card";
import { DownloadCloud } from "react-feather";
import { Resource } from "@/features/resources/types";

export const FileCard = ({ resource }: { resource: Resource }) => {
	const getValidUrl = (url: string) => {
		if (!url.startsWith("http://") && !url.startsWith("https://")) {
			return `https://${url}`;
		}
		return url;
	};

	const validUrl = resource.url ? getValidUrl(resource.url) : "#";

	return (
		<Card className="p-3 border-gray-200">
			<CardContent className="p-2 flex items-center gap-3">
				<div className="w-9 h-9 rounded-lg flex items-center justify-center overflow-hidden">
					<img
						src={"/default-placeholder.png"}
						alt={resource.title}
						className="object-cover w-full h-full"
					/>
				</div>
				<div className="flex-1 w-1/2">
					<CardTitle className="text-sm font-semibold">
						{resource.title}
					</CardTitle>
					<CardDescription className="text-xs text-gray-400">
						{resource.url}
					</CardDescription>
				</div>
				<div className="w-12 lg:w-32 h-12 bg-white border rounded-lg flex items-center justify-center">
					<a
						href={validUrl}
						target="_blank"
						rel="noopener noreferrer"
						className="lg:flex lg:items-center lg:justify-between lg:px-5 lg:w-full lg:h-full a"
						aria-label={`Visit ${resource.title}`}
					>
						<p className="text-sm hidden font-medium  lg:block text-black-900">
							Download
						</p>
						<DownloadCloud className="w-5 h-5 lg:w-4 lg:h-4 text-black-900" />
					</a>
				</div>
			</CardContent>
		</Card>
	);
};
