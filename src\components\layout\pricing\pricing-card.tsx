import { Button } from "@/components/ui/button";
import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Check } from "react-feather";

type PricingCardProps = {
	title: string;
	price: number;
	period: string;
	features: string[];
	isPrimary?: boolean;
	onGetPlan?: () => void;
};

const PricingCard = ({
	title,
	price,
	period,
	features,
	isPrimary = false,
	onGetPlan = () => {},
}: PricingCardProps) => {
	return (
		<Card
			className={`rounded-xl w-full md:w-80 shadow-lg ${isPrimary ? "bg-accent text-white" : "bg-white"} flex flex-col`}
		>
			<CardHeader className="pb-2">
				<h3 className="font-bold">{title}</h3>
				<div className="flex items-end gap-1">
					<span className="text-xl font-bold">Rs.</span>
					<span className="text-5xl font-bold">{price}</span>
					<span
						className={`text-base ${isPrimary ? "text-gray-400" : "text-gray-500"}`}
					>
						/{period}
					</span>
				</div>
			</CardHeader>

			<CardContent className="space-y-6 pt-6 flex-grow">
				{features.map((feature, index) => (
					<div key={index} className="flex items-center gap-3">
						<div className="p-1">
							<Check
								className={`h-4 w-4 ${
									isPrimary ? "text-white" : "text-purple-600"
								}`}
							/>
						</div>
						<span className="text-sm">{feature}</span>
					</div>
				))}
			</CardContent>

			<CardFooter className="mt-auto pb-6">
				<Button
					onClick={onGetPlan}
					className={`w-full py-6 ${
						isPrimary
							? "bg-white hover:bg-gray-100 text-black"
							: "bg-accent text-white"
					}`}
				>
					Get
				</Button>
			</CardFooter>
		</Card>
	);
};

export default PricingCard;
