import { But<PERSON> } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { createLazyFileRoute } from "@tanstack/react-router";
import { loginValidation, LoginValidation } from "@/features/auth/schema";

const Login = () => {
	const form = useForm<LoginValidation>({
		resolver: zodResolver(loginValidation),
		defaultValues: {
			email: "",
			password: "",
		},
	});

	const onSubmit = (values: LoginValidation) => {
		console.log(values);
	};
	return (
		<div className="h-full flex flex-col gap-y-4 p-4 sm:p-8 justify-center items-center bg-white rounded-lg">
			<h1>Reset Password</h1>
			<p className="text-gray-600 text-center  max-w-[400px]">
				Type your authorized email address OR phone number to receive reset
				password link.
			</p>
			<div className="w-full flex flex-col gap-y-2">
				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="gap-y-4 flex flex-col"
					>
						<FormField
							control={form.control}
							name="email"
							render={({ field }) => (
								<FormItem className="space-y-0">
									<FormLabel className="text-gray-500 text-xs sm:text-sm">
										Email
									</FormLabel>
									<FormControl>
										<Input placeholder="Email" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<Button type="submit" className="w-full text-xs sm:text-sm ">
							Send Link
						</Button>
					</form>
				</Form>
			</div>
		</div>
	);
};

export const Route = createLazyFileRoute("/account/_layout/resetpassword")({
	component: Login,
});
