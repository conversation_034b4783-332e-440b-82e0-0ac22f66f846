import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { ArrowLeft } from "react-feather";
import { Label } from "@/components/ui/label";
import { ICONS } from "@/lib/assets/images";
import CreditCardPayment from "@/components/layout/pricing/credit-payment";

type CheckoutPageProps = {
	planType?: "daily" | "subscription";
	price?: number;
	onBack: () => void;
};

const CheckoutPage = ({
	planType = "subscription",
	price = 200,
	onBack,
}: CheckoutPageProps) => {
	const [selectedPeriod, setSelectedPeriod] = useState("1 Month");
	const [showTransferDetails, setShowTransferDetails] = useState(false);
	const [showCreditCardPayment, setShowCreditCardPayment] = useState(false);
	const [paymentMethod, setPaymentMethod] = useState<string | null>(null);

	const isPeriodSelectionVisible = planType === "subscription";

	const handlePaymentMethodSelection = (method: string) => {
		setPaymentMethod(method);
		if (method === "transfer") {
			setShowTransferDetails(true);
			setShowCreditCardPayment(false);
		} else if (method === "credit-card") {
			setShowCreditCardPayment(true);
			setShowTransferDetails(false);
		}
	};

	// If showing the credit card payment view
	if (showCreditCardPayment) {
		return (
			<CreditCardPayment
				price={price}
				period={selectedPeriod}
				onBack={() => setShowCreditCardPayment(false)}
			/>
		);
	}

	return (
		<div className="min-h-screen bg-gradient-to-r from-[#CBBBFF] via-[#CBBBFF20] to-[#FFFFFF00] flex flex-col items-center justify-center p-6">
			<div className="absolute top-12 left-14 mt-1">
				<img
					src={ICONS.logoexpanded}
					alt="Logo"
					className="w-32 sm:w-36 lg:w-40"
				/>
			</div>
			<div>
				<Card className="max-w-2xl bg-white shadow-md rounded-lg">
					<CardHeader className="pb-2">
						<Button
							variant="link"
							size="sm"
							onClick={onBack}
							className="w-fit h-8 p-0 flex items-center text-gray-500 hover:text-gray-700 mb-2"
						>
							<ArrowLeft className="h-4 w-4 mr-1" />
							<span className="text-sm">Back</span>
						</Button>
					</CardHeader>

					<CardContent className="space-y-6">
						<div className="flex justify-between items-center">
							<div>
								<h1 className="text-[40px] font-bold">Your Cart</h1>
								<p className="text-sm font-medium text-gray-600 mt-2">
									Please proceed with your payment gateway
								</p>
							</div>
							<div className="text-right">
								<span className="text-sm">Rs.</span>
								<span className="text-[40px] font-bold"> {price}</span>
								<span className="text-sm text-gray-500"> total</span>
							</div>
						</div>

						{isPeriodSelectionVisible && (
							<div>
								<Label className="block text-sm font-medium text-gray-700">
									Select Period
								</Label>
								<Select
									value={selectedPeriod}
									onValueChange={setSelectedPeriod}
								>
									<SelectTrigger className="w-full h-14 text-base text-gray-500 bg-white rounded-xl shadow-sm">
										<SelectValue placeholder="Select period" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="1 Month">1 Month</SelectItem>
										<SelectItem value="3 Months">3 Months</SelectItem>
										<SelectItem value="6 Months">6 Months</SelectItem>
										<SelectItem value="1 Year">1 Year</SelectItem>
									</SelectContent>
								</Select>
							</div>
						)}

						<div>
							<Label className="block text-sm text-gray-600 font-medium mb-2">
								Please select a payment method:
							</Label>

							{!showTransferDetails ? (
								<div className="grid grid-cols-2 gap-3">
									<Button
										variant="outline"
										className={`w-full rounded-xl border h-auto py-6 text-center justify-center whitespace-normal text-sm sm:text-base leading-snug hover:bg-accent hover:text-white ${
											paymentMethod === "credit-card"
												? "bg-accent text-white"
												: "bg-white"
										}`}
										onClick={() => handlePaymentMethodSelection("credit-card")}
									>
										Get instant access with Credit Card
									</Button>

									<Button
										variant="outline"
										className={`w-full rounded-xl h-auto py-6 text-center justify-center whitespace-normal text-sm sm:text-base leading-snug  hover:bg-accent hover:text-white ${
											paymentMethod === "transfer"
												? "bg-accent text-white"
												: "bg-white"
										}`}
										onClick={() => handlePaymentMethodSelection("transfer")}
									>
										Transfer and send screenshot to WhatsApp
									</Button>
								</div>
							) : (
								<div className="space-y-6">
									<div className="grid grid-cols-2 gap-3">
										<Button
											variant="outline"
											className="w-full rounded-xl border h-auto py-6 text-center justify-center whitespace-normal text-sm sm:text-base leading-snug"
											onClick={() =>
												handlePaymentMethodSelection("credit-card")
											}
										>
											Get instant access with Credit Card
										</Button>

										<Button
											variant="outline"
											className="w-full rounded-xl h-auto py-6 bg-accent text-white text-center justify-center whitespace-normal text-sm sm:text-base leading-snug"
											onClick={() => handlePaymentMethodSelection("transfer")}
										>
											Transfer and send screenshot to WhatsApp
										</Button>
									</div>

									<div className="mt-6 p-4 border border-gray-200 rounded-lg">
										<p className="font-medium mb-2">
											Please Send your payment to the following account:
										</p>
										<div className="space-y-1 text-sm">
											<p>Bank Name:</p>
											<p>Account Title: Hadi Khan</p>
											<p>Account #:</p>
											<p>IBAN:</p>
											<p className="text-green-500 mt-2 text-sm">
												*Please send us proof of your payment along with your ID
												at XXXX XXXXXXX
											</p>
										</div>
									</div>
								</div>
							)}
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
};

export default CheckoutPage;
