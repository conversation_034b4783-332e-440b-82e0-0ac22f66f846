/** @type {import('tailwindcss').Config} */
export default {
	darkMode: ["class"],
	content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
	theme: {
		extend: {
			borderRadius: {
				lg: "var(--radius)",
				md: "calc(var(--radius) - 2px)",
				sm: "calc(var(--radius) - 4px)",
			},
			colors: {
				background: "hsl(var(--background))",
				foreground: "hsl(var(--foreground))",
				card: {
					DEFAULT: "hsl(var(--card))",
					foreground: "hsl(var(--card-foreground))",
				},
				popover: {
					DEFAULT: "hsl(var(--popover))",
					foreground: "hsl(var(--popover-foreground))",
				},
				primary: {
					DEFAULT: "hsl(var(--primary))",
					foreground: "hsl(var(--primary-foreground))",
				},
				secondary: {
					DEFAULT: "hsl(var(--secondary))",
					foreground: "hsl(var(--secondary-foreground))",
				},
				tertiary: {
					DEFAULT: "hsl(var(--tertiary))",
					foreground: "hsl(var(--tertiary-foreground))",
				},
				muted: {
					DEFAULT: "hsl(var(--muted))",
					foreground: "hsl(var(--muted-foreground))",
				},
				accent: {
					DEFAULT: "hsl(var(--accent))",
					foreground: "hsl(var(--accent-foreground))",
				},
				destructive: {
					DEFAULT: "hsl(var(--destructive))",
					foreground: "hsl(var(--destructive-foreground))",
				},
				border: "hsl(var(--border))",
				input: "hsl(var(--input))",
				ring: "hsl(var(--ring))",
				chart: {
					1: "hsl(var(--chart-1))",
					2: "hsl(var(--chart-2))",
					3: "hsl(var(--chart-3))",
					4: "hsl(var(--chart-4))",
					5: "hsl(var(--chart-5))",
				},
			},
			fontFamily: {
				sans: ['"Inter"', "sans-serif"],
			},
			keyframes: {
				growAndFade: {
					"0%": {
						opacity: "0.35",
						transform: "scale(0.1)",
					},
					"100%": {
						opacity: "0",
						transform: "scale(0.7)",
					},
				},
				"collapse-down": {
					from: { height: 0, opacity: 0 },
					to: { height: "var(--radix-collapsible-content-height)", opacity: 1 },
				},
				"collapse-up": {
					from: { height: "var(--radix-collapsible-content-height)", opacity: 1 },
					to: { height: 0, opacity: 0 },
				},
			},
			animation: {
				"grow-and-fade": "growAndFade 2.5s infinite ease-out",
				"collapse-down": "collapse-down 300ms ease-out",
				"collapse-up": "collapse-up 300ms ease-out",
			},
		},
	},
	plugins: [require("tailwindcss-animate")],
};
