"use client";

import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>Content,
	TooltipTrigger,
	TooltipProvider,
} from "@/components/ui/tooltip";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Bell } from "react-feather";

const Notifications = () => {
	return (
		<DropdownMenu>
			<TooltipProvider disableHoverableContent>
				<Tooltip delayDuration={100}>
					<TooltipTrigger asChild>
						<DropdownMenuTrigger asChild>
							{/* <Button
                variant="outline"
                className=" flex gap-x-2 p-0 lg:items-center rounded-full text-icongray border-slate-200 size-10 lg:size-12 hover:bg-white"
              > */}
							<Bell
								size={48}
								className="border border-gray-200 text-gray-600 rounded-full p-3 hover:cursor-pointer"
							/>
							{/* </Button> */}
						</DropdownMenuTrigger>
					</TooltipTrigger>
					<TooltipContent side="bottom">Notifications</TooltipContent>
				</Tooltip>
			</TooltipProvider>

			<DropdownMenuContent className="w-56" align="end" forceMount>
				Notifications content
			</DropdownMenuContent>
		</DropdownMenu>
	);
};

export default Notifications;
