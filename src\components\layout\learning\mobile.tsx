import { resourceTypes, subjects } from "@/features/learning/constants";
import { MobileVideoSection } from "@/components/layout/learning/mobile-video";
import { MobileFileSection } from "@/components/layout/learning/mobile-file";
import { MobileLinkSection } from "@/components/layout/learning/mobile-link";
import { Search } from "react-feather";
import { Resource } from "@/features/resources/types";

type MobileLearningViewProps = {
	setResourceType?: (type: string) => void;
	resources: (Resource[] | undefined)[];
	selectedSubject: string;
	setSelectedSubject: React.Dispatch<React.SetStateAction<string>>;
};

const MobileLearningView = ({
	setResourceType,
	resources,
	selectedSubject,
	setSelectedSubject,
}: MobileLearningViewProps) => {
	const groupedResources = resourceTypes.reduce(
		(acc, type) => {
			acc[type.id] = resources.find((resource) =>
				resource?.some(
					(res) => res.type === type.id && res.subject.includes(selectedSubject)
				)
			);
			return acc;
		},
		{} as Record<string, Resource[] | undefined>
	);

	return (
		<div className="flex flex-col h-full">
			<div className="flex items-center px-4 py-2 gap-2 overflow-x-auto no-scrollbar">
				<div className="flex-1 flex gap-2 overflow-x-auto no-scrollbar">
					{subjects.map((subject) => (
						<button
							key={subject.id}
							onClick={() => setSelectedSubject(subject.id)}
							className={`px-4 py-2 rounded-full whitespace-nowrap text-sm font-medium 
                ${
									selectedSubject === subject.id
										? "bg-accent text-white"
										: "text-gray-400"
								}`}
						>
							{subject.label}
						</button>
					))}
				</div>
				<Search className="w-6 h-6 flex-shrink-0 mx-4" />
			</div>

			<MobileVideoSection
				resources={groupedResources["video"] ?? []}
				selectedSubject={selectedSubject}
				setResourceType={setResourceType}
			/>
			<MobileFileSection
				resources={groupedResources["file"] ?? []}
				selectedSubject={selectedSubject}
				setResourceType={setResourceType}
			/>
			<MobileLinkSection
				resources={groupedResources["link"] ?? []}
				selectedSubject={selectedSubject}
				setResourceType={setResourceType}
			/>
			{!groupedResources["video"] &&
				!groupedResources["file"] &&
				!groupedResources["link"] && (
					<p className="text-center mt-6 text-gray-400">No resources found</p>
				)}
		</div>
	);
};

export default MobileLearningView;
