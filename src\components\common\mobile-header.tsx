import { routeList } from "@/lib/route-list";
import { useRouter } from "@tanstack/react-router";
import React from "react";
import { ChevronLeft } from "react-feather";

type MobileHeaderProps = {
	title?: string;
	resourceType?: string;
	rightSide?: React.ReactNode;
};

const MobileHeader = ({
	title,
	resourceType,
	rightSide: RightSide,
}: MobileHeaderProps) => {
	const { history } = useRouter();
	const pathname = window.location.pathname;
	const currentRoute = routeList
		.find((menu) => menu.menus.some((m) => m.href === pathname))
		?.menus.find((m) => m.href === pathname);

	const label = currentRoute?.label;

	const handleClick = () => {
		history.go(-1);
	};

	return (
		<div className="fixed w-full flex items-center justify-between bg-background h-[80px] px-4">
			<ChevronLeft size={24} onClick={handleClick} />
			<h6 className="font-medium lg:hidden">
				{resourceType === "video"
					? "Videos"
					: resourceType === "files"
						? "Files"
						: resourceType === "link"
							? "Links"
							: (title ?? label)}
			</h6>
			{RightSide ?? <ChevronLeft className="invisible" size={24} />}
		</div>
	);
};

export default MobileHeader;
