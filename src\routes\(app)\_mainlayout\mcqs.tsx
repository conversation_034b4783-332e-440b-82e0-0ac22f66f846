import { createFileRoute } from "@tanstack/react-router";
import { useState, useEffect } from "react";
import useMediaQuery from "@/hooks/use-media-query";
import { sampleTest } from "@/features/mcqs/constants";
import TestLayout from "@/components/layout/mcqs/test-layout";
import MCQMobileLayout from "@/components/layout/mcqs/mcq-mobile-layout";
import { Test, MCQ } from "@/features/mcqs/types";
import { MockTestResponse, MockTestMCQ } from "@/features/tests/services";
import { useToast } from "@/hooks/use-toast";

// Convert API format to the Test format used by the components
const convertApiDataToTest = (apiData: MockTestResponse): Test => {
	const mcqs: MCQ[] = [];

	// Flatten the MCQs by subject into a single array
	Object.entries(apiData.mcqsBySubject).forEach(([subject, mcqList]) => {
		mcqList.forEach((apiMcq: MockTestMCQ) => {
			// Ensure the subject is a valid one from our constants
			const validSubject = subject as MCQ["subject"];

			mcqs.push({
				id: apiMcq._id,
				question: apiMcq.title,
				options: apiMcq.options,
				correctAnswer: apiMcq.answer,
				subject: validSubject,
				tag: apiMcq.type,
				description: apiMcq.explanation,
			});
		});
	});

	// Format duration based on minutes and seconds from the API response
	// We'll only use totalSeconds since it's more accurate for time calculation
	const totalSeconds = apiData.time.seconds || 0;

	// The API provides both minutes and total seconds, but we'll use totalSeconds for accuracy
	const hours = Math.floor(totalSeconds / 3600);
	const remainingMinutes = Math.floor((totalSeconds % 3600) / 60);
	const remainingSeconds = totalSeconds % 60;

	// Format as HH:MM:SS
	const formattedDuration = `${hours.toString().padStart(2, "0")}:${remainingMinutes
		.toString()
		.padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;

	return {
		id: "api-test",
		title: `${apiData.requestedParams.entryTest} Mock Test`,
		totalQuestions: apiData.count,
		duration: formattedDuration,
		liveCheck: false,
		mcqs: mcqs,
	};
};

const Page = () => {
	const isDesktop = useMediaQuery("(min-width: 1024px)");
	const [test, setTest] = useState<Test>(sampleTest);
	const [liveCheckEnabled, setLiveCheckEnabled] = useState(false);
	const [selectedAnswers, setSelectedAnswers] = useState<
		Record<string, number>
	>({});
	const [answerStatus, setAnswerStatus] = useState<Record<string, boolean>>({});
	const [showResults, setShowResults] = useState(false);
	const [testTimesUp,setTestTimesUp] = useState(false)
	const { toast } = useToast();

	// Get test data from localStorage
	useEffect(() => {
		const storedTestData = localStorage.getItem("currentTestData");

		if (storedTestData) {
			try {
				const apiTestData = JSON.parse(storedTestData) as MockTestResponse;
				const convertedTest = convertApiDataToTest(apiTestData);
				setTest(convertedTest);
				setLiveCheckEnabled(false); // Reset live check for new test
				setSelectedAnswers({}); // Reset answers for new test
				setAnswerStatus({}); // Reset status for new test
				setShowResults(false); // Reset results for new test

				// Clear the localStorage to avoid reusing the same test data accidentally
				// localStorage.removeItem('currentTestData');
				// Commented out for now in case user refreshes the page
			} catch (error) {
				console.error("Failed to parse test data:", error);
				toast({
					title: "Error",
					description: "Failed to load test data. Using sample test instead.",
					variant: "destructive",
				});
				// Fall back to sample test if data can't be parsed
				setTest(sampleTest);
			}
		}
	}, [toast]);

	useEffect(() => {
		if (liveCheckEnabled && Object.keys(selectedAnswers).length > 0) {
			const newStatus: Record<string, boolean> = {};
			Object.entries(selectedAnswers).forEach(([questionId, optionIndex]) => {
				const mcq = test.mcqs.find((m) => m.id === questionId);
				if (mcq) {
					newStatus[questionId] = optionIndex === mcq.correctAnswer;
				}
			});

			setAnswerStatus(newStatus);
			setShowResults(true);
		}
	}, [liveCheckEnabled, selectedAnswers, test.mcqs]);

	const handleAnswerSelect = (questionId: string, optionIndex: number) => {
		if (showResults && selectedAnswers[questionId] !== undefined) return;

		const mcq = test.mcqs.find((m) => m.id === questionId);
		if (!mcq) return;

		setSelectedAnswers((prev) => ({
			...prev,
			[questionId]: optionIndex,
		}));

		if (liveCheckEnabled) {
			// Check if the selected answer matches the correctAnswer in the MCQ
			const isCorrect = optionIndex === mcq.correctAnswer;
			setAnswerStatus((prev) => ({
				...prev,
				[questionId]: isCorrect,
			}));
			setShowResults(true);
		}
	};

	const handleCheckAnswers = () => {
		const newStatus: Record<string, boolean> = {};

		Object.entries(selectedAnswers).forEach(([questionId, optionIndex]) => {
			const mcq = test.mcqs.find((m) => m.id === questionId);
			if (mcq) {
				// Compare the selected answer with the correct answer
				newStatus[questionId] = optionIndex === mcq.correctAnswer;
			}
		});

		setAnswerStatus(newStatus);
		setShowResults(true);
	};

	return isDesktop ? (
		<TestLayout
			test={test}
			liveCheckEnabled={liveCheckEnabled}
			setLiveCheckEnabled={setLiveCheckEnabled}
			selectedAnswers={selectedAnswers}
			answerStatus={answerStatus}
			showResults={showResults}
			setShowResults={setShowResults}
			onAnswerSelect={handleAnswerSelect}
			onCheckAnswers={handleCheckAnswers}
			testTimesUp={testTimesUp}
			setTestTimesUp={setTestTimesUp}
		/>
	) : (
		<MCQMobileLayout
			test={test}
			liveCheckEnabled={liveCheckEnabled}
			setLiveCheckEnabled={setLiveCheckEnabled}
			selectedAnswers={selectedAnswers}
			answerStatus={answerStatus}
			showResults={showResults}
			setShowResults={setShowResults}
			onAnswerSelect={handleAnswerSelect}
			onCheckAnswers={handleCheckAnswers}
			testTimesUp={testTimesUp}
		/>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/mcqs")({
	component: Page,
});

export default Page;
