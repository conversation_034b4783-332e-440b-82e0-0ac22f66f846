import { api } from "@/lib/api";
import { APIResponse } from "@/lib/api/types";
import { AxiosResponse } from "axios";

export interface MockTestRequest {
	entryTest: string;
}

export interface MockTestMCQ {
	_id: string;
	subject: string;
	type: string;
	title: string;
	topic: string;
	difficulty: number;
	repitition: boolean;
	resource?: string;
	explanation: string;
	entryTest: string[];
	options: string[];
	answer: number;
	chapter: number;
}

export interface MockTestResponse {
	mcqsBySubject: {
		[subject: string]: MockTestMCQ[];
	};
	count: number;
	subjectCounts: {
		[subject: string]: number;
	};
	time: {
		minutes: number;
		seconds: number;
	};
	requestedParams: {
		entryTest: string;
		weightages: {
			[subject: string]: number;
		};
	};
}

export const generateMockTest = async (
	request: MockTestRequest
): Promise<AxiosResponse<APIResponse<MockTestResponse>>> => {
	return await api.post<APIResponse<MockTestResponse>>(
		"/quiz/generate-mock",
		request
	);
};
