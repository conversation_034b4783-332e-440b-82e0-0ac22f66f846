import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { ChevronLeft } from "react-feather";

interface SidebarToggleProps {
	isOpen: boolean | undefined;
	setIsOpen?: () => void;
}

export function SidebarToggle({ isOpen, setIsOpen }: SidebarToggleProps) {
	return (
		<div className="invisible lg:visible absolute top-32 -right-[16px] z-20">
			<Button
				onClick={() => setIsOpen?.()}
				className="rounded-md w-4 h-8 rounded-l-none rounded-r-lg border-2 border-l-0 border-black/15 bg-white"
				variant="outline"
				size="icon"
			>
				<ChevronLeft
					className={cn(
						"h-4 w-4 transition-transform ease-in-out duration-700",
						isOpen === false ? "rotate-180" : "rotate-0"
					)}
				/>
			</Button>
		</div>
	);
}
