import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { resourceTypes } from "@/features/learning/constants";
import ResourceContent from "@/components/layout/learning/resource-content";
import { Resource } from "@/features/resources/types";

type SelectedSubjectProps = {
	resources: (Resource[] | undefined)[];
	isLoading: boolean;
};

export const ResourceTabs = ({
	resources,
	isLoading,
}: SelectedSubjectProps) => {
	const groupedResources = resourceTypes.reduce(
		(acc, type) => {
			acc[type.id] = resources.find((resource) =>
				resource?.some((res) => res.type === type.id)
			);
			return acc;
		},
		{} as Record<string, Resource[] | undefined>
	);
	return (
		<Tabs
			defaultValue={resourceTypes[0]?.id || ""}
			className="w-full mt-1"
			aria-label="Resource Tabs"
		>
			<TabsList className="mb-6">
				{resourceTypes.map((type) => (
					<TabsTrigger
						key={type.id}
						value={type.id}
						className="px-6 font-medium text-sm shadow-none data-[state=active]:text-gray-900 data-[state=active]:shadow-none"
					>
						{type.label}
					</TabsTrigger>
				))}
			</TabsList>

			{isLoading ? (
				<p>Loading...</p>
			) : (
				resourceTypes.map((type) => (
					<TabsContent key={type.id} value={type.id} className="mt-0">
						<ResourceContent
							type={type.id}
							resources={groupedResources[type.id] ?? []}
						/>
					</TabsContent>
				))
			)}
		</Tabs>
	);
};
