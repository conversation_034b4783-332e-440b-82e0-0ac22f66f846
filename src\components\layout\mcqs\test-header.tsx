import { ChevronLeft, Bookmark } from "react-feather";
import { Test } from "@/features/mcqs/types";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { Switch } from "@/components/ui/switch";
import { Timer } from "@/components/ui/timer";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

type TestHeaderProps = {
	test: Test;
	liveCheckEnabled: boolean;
	setLiveCheckEnabled: (enabled: boolean) => void;
	setTestTimesUp: (enabled: boolean) => void;
};

const TestHeader = ({
	test,
	liveCheckEnabled,
	setLiveCheckEnabled,
	setTestTimesUp,
}: TestHeaderProps) => {
	const [isBookmarked, setIsBookmarked] = useState(false);
	const { toast } = useToast();

	// Parse test duration to get minutes and seconds for the timer
	const parseTestDuration = () => {
		try {
			// Split duration into hours, minutes, seconds
			const parts = test.duration.split(":");
			// Extract hours, minutes, seconds - use nullish coalescing to ensure we have valid numbers
			const hours = parseInt(parts[0] ?? "0", 10);
			const minutes = parseInt(parts[1] ?? "0", 10);
			const seconds = parseInt(parts[2] ?? "0", 10);

			// Calculate total minutes for the timer
			return {
				minutes: hours * 60 + minutes,
				seconds: seconds,
			};
		} catch (error) {
			console.error("Error parsing test duration:", error);
			return { minutes: 30, seconds: 0 }; // Default to 30 minutes if parsing fails
		}
	};

	const { minutes, seconds } = parseTestDuration();

	const handleTimeEnd = () => {
		setTestTimesUp(true)
		toast({
			title: "Time's up!",
			description: "Your test time has ended. Please submit your answers.",
			variant: "destructive",
		});
	};

	const toggleBookmark = () => {
		setIsBookmarked((prev) => !prev);
	};

	return (
		<div className="flex items-center justify-between p-4 ">
			<div className="flex items-center gap-2">
				<Button variant="icon" className="p-2 [&_svg]:size-5">
					<ChevronLeft className="w-5 h-5" />
				</Button>
				<div>
					<div className="flex items-center gap-2">
						<h3 className="font-extrabold">{test.title}</h3>
						<p className="text-sm px-2 py-0.5 font-semibold border border-purple-300 bg-purple-100 text-accent rounded-full">
							timed
						</p>
					</div>
					<div className="flex items-center gap-2 text-sm text-gray-500">
						<span>Question 1 of {test.totalQuestions}</span>
						<Button
							variant="link"
							size="sm"
							className="underline text-gray-500"
						>
							see info
						</Button>
					</div>
				</div>
			</div>
			<div className="flex items-center gap-4">
				<div className="w-10 h-10 flex items-center bg-white justify-center rounded-full border">
					<Button
						variant="icon"
						onClick={toggleBookmark}
						className="[&_svg]:size-5"
					>
						<Bookmark
							className={`w-5 h-5  ${isBookmarked ? "fill-accent" : ""}`}
						/>
					</Button>
				</div>
				<div
					className={cn(
						"flex items-center gap-2 px-3 py-1 rounded-md transition-all",
						liveCheckEnabled
							? "bg-accent/10 border border-accent"
							: "bg-white border border-gray-200 hover:border-gray-300"
					)}
				>
					<div className="flex items-center space-x-2">
						<Switch
							id="live-check"
							checked={liveCheckEnabled}
							onCheckedChange={setLiveCheckEnabled}
							className="data-[state=checked]:bg-accent"
						/>
						<p
							className={cn(
								"text-sm font-semibold",
								liveCheckEnabled ? "text-accent" : "text-gray-600"
							)}
						>
							Live Check
						</p>
					</div>
				</div>
				<div className="flex items-center gap-2 bg-accent text-white py-2.5 px-4 rounded-full">
					<Timer
						initialMinutes={minutes}
						initialSeconds={seconds}
						onTimeEnd={handleTimeEnd}
						className="text-white"
					/>
				</div>
			</div>
		</div>
	);
};

export default TestHeader;
