import { SectionProps } from "@/features/learning/types";
import { FileCard } from "@/components/layout/learning/file-card";
import { SheetButton } from "@/components/layout/learning/resource-sheet";

export const MobileFileSection = ({
	selectedSubject,
	setResourceType,
	resources,
}: SectionProps) => {
	if (resources.length === 0) return null;

	return (
		<div className="mt-6">
			<div className="px-4 flex justify-between items-center">
				<h3>Files</h3>
				<SheetButton
					resources={resources}
					resourceType="files"
					selectedSubject={selectedSubject}
					setResourceType={setResourceType}
				/>
			</div>
			<div className="mt-2 px-4 space-y-2">
				{resources.map((file) => (
					<FileCard key={file.id} resource={file} />
				))}
			</div>
		</div>
	);
};
