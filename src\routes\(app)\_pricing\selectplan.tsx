import { createFileRoute } from '@tanstack/react-router'
import PricingPage from '@/components/layout/pricing/plan'
import useMediaQuery from '@/hooks/use-media-query'
import MobilePricingPage from '@/components/layout/pricing/mobile-plan'

const Page = () => {
  const isDesktop = useMediaQuery('(min-width: 768px)')
  return isDesktop ? <PricingPage /> : <MobilePricingPage />
}

export const Route = createFileRoute('/(app)/_pricing/selectplan')({
  component: Page,
})
export default Page
