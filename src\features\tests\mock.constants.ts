// Mock test data types and constants
export interface MockTestCard {
	id: string;
	title: string;
	description: string;
	icon: string;
	entryTestCode: string; // Added this field for API request
}

// Mock test data
export const mockTests: MockTestCard[] = [
	{
		id: "1",
		title: "NET - NUST Engineering Test",
		description:
			"Engineering NET for NUST. There are 100 Math MCQs, 60 Physics and 30 English.",
		icon: "/assets/icons/nust.png",
		entryTestCode: "NET", // This is the value to be sent to the API
	},
	{
		id: "2",
		title: "ECAT - UET/Gov Engineering Test",
		description:
			"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec quis tristique neque. Integer commodo suscipit arcu a maximus. Mauris dapibus nulla venenatis felis convallis congue. Nunc sollicitudin, urna vitae mollis auctor. Phasellus nec dui blandit, gravida dui vel, ornare quam.",
		icon: "/assets/icons/uet.png",
		entryTestCode: "ECAT", // This is the value to be sent to the API
	},
];
