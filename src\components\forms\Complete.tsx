import { EmptyValidation } from "@/features/auth/schema";
import { But<PERSON> } from "@/components/ui/button";
import { useFormContext } from "react-hook-form";
import { useAuthStore } from "@/features/auth/store";
import { useShallow } from "zustand/react/shallow";

const Complete = ({
	formRef,
}: {
	formRef: React.RefObject<HTMLFormElement>;
}) => {
	const messages = [
		"📚 Check your current knowledge level.",
		"🔎 Identify areas to focus on for maximum improvement.",
		"📋 Get a preview of what actual test questions look like.",
	];
	const form = useFormContext<EmptyValidation>();

	// Get Firebase user for accessing user name (from both social login and registration)
	const { user } = useAuthStore(
		useShallow((state) => ({
			user: state.user,
		}))
	);

	return (
		<div className="h-full flex flex-col gap-y-6 p-4 sm:p-8 justify-center items-center bg-white rounded-lg">
			<h1>Congratulations 🎉</h1>
			<p className="text-gray-600 text-center max-w-[400px]">
				{`Congratulations${user?.displayName ? ", " + user?.displayName : ""}! You're ready to begin.`}
			</p>
			<div className="w-full flex flex-col gap-y-4">
				<p className="font-bold">Use Parhlai to:</p>
				{messages.map((message) => (
					<p className="text-gray-400" key={message}>
						{message}
					</p>
				))}
			</div>
			<p className="font-bold">Would you like to attempt a Trial Test?</p>
			<div className="flex w-full gap-x-2">
				<Button
					onClick={() => {
						form.setValue("takeTrialTest", false);
						formRef?.current?.dispatchEvent(
							new Event("submit", { cancelable: true, bubbles: true })
						);
					}}
					variant="outline"
					className="flex-1 text-xs sm:text-sm"
				>
					Skip for Now
				</Button>
				<Button
					onClick={() => {
						form.setValue("takeTrialTest", true);
						formRef?.current?.dispatchEvent(
							new Event("submit", { cancelable: true, bubbles: true })
						);
					}}
					className="flex-1 text-xs sm:text-sm"
				>
					Start Trial Test
				</Button>
			</div>
		</div>
	);
};

export default Complete;
