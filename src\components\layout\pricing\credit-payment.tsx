import { ArrowLeft } from "react-feather";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useState } from "react";
import { ICONS } from "@/lib/assets/images";

type CreditCardPaymentProps = {
	price: number;
	period: string;
	onBack: () => void;
};

const CreditCardPayment = ({
	price,
	period,
	onBack,
}: CreditCardPaymentProps) => {
	const [selectedPaymentMethod, setSelectedPaymentMethod] =
		useState("credit-card");

	const handlePaymentMethodClick = (method: string) => {
		setSelectedPaymentMethod(method);
	};

	return (
		<div className="min-h-screen bg-gradient-to-r from-[#CBBBFF] via-[#CBBBFF20] to-[#FFFFFF00] flex flex-col items-center justify-center p-6">
			<div className="absolute top-12 left-14 mt-1">
				<img
					src={ICONS.logoexpanded}
					alt="Logo"
					className="w-32 sm:w-36 lg:w-40"
				/>
			</div>
			<div className="w-full max-w-2xl">
				<Card className="bg-white shadow-md rounded-lg">
					<CardHeader className="pb-2">
						<Button
							variant="link"
							size="sm"
							onClick={onBack}
							className="w-fit h-8 p-0 flex items-center text-gray-500 hover:text-gray-700 mb-2"
						>
							<ArrowLeft className="h-4 w-4 mr-1" />
							<span className="text-sm">Back</span>
						</Button>
					</CardHeader>

					<CardContent className="space-y-6">
						<div className="flex justify-between items-center">
							<div>
								<h1 className="text-[40px] font-bold">Payment</h1>
								<p className="text-sm font-medium text-gray-600 mt-2">
									Please choose a payment method.
								</p>
							</div>
							<div className="text-right">
								<span className="text-sm">Rs.</span>
								<span className="text-[40px] font-bold">{price}</span>
								<div>
									<div className="text-sm text-gray-500">
										for {period.toLowerCase()}
									</div>
								</div>
							</div>
						</div>

						<RadioGroup
							value={selectedPaymentMethod}
							onValueChange={setSelectedPaymentMethod}
							className="space-y-3"
						>
							<div
								className={`flex items-center justify-between p-4 border rounded-xl cursor-pointer transition-colors ${
									selectedPaymentMethod === "credit-card"
										? "border-accent bg-purple-50"
										: "border-gray-200 hover:bg-gray-50"
								}`}
								onClick={() => handlePaymentMethodClick("credit-card")}
							>
								<div className="flex items-center gap-3">
									<img
										src={ICONS.creditcard}
										alt="Credit Card"
										className="h-10 w-10 shadow-sm rounded-xl"
									/>
									<Label
										htmlFor="credit-card"
										className="font-medium cursor-pointer"
									>
										Credit card
									</Label>
								</div>
								<RadioGroupItem value="credit-card" id="credit-card" />
							</div>

							<div
								className={`flex items-center justify-between p-4 border rounded-xl cursor-pointer transition-colors ${
									selectedPaymentMethod === "paypal"
										? "border-accent bg-purple-50"
										: "border-gray-200 hover:bg-gray-50"
								}`}
								onClick={() => handlePaymentMethodClick("paypal")}
							>
								<div className="flex items-center gap-3">
									<img
										src={ICONS.paypal}
										alt="PayPal"
										className="h-10 w-10 shadow-sm rounded-xl"
									/>
									<Label
										htmlFor="paypal"
										className="font-medium cursor-pointer"
									>
										Paypal
									</Label>
								</div>
								<RadioGroupItem value="paypal" id="paypal" />
							</div>

							<div
								className={`flex items-center justify-between p-4 border rounded-xl cursor-pointer transition-colors ${
									selectedPaymentMethod === "apple-pay"
										? "border-accent bg-purple-50"
										: "border-gray-200 hover:bg-gray-50"
								}`}
								onClick={() => handlePaymentMethodClick("apple-pay")}
							>
								<div className="flex items-center gap-3">
									<img
										src={ICONS.applepay}
										alt="Apple Pay"
										className="h-10 w-10 shadow-sm rounded-xl"
									/>
									<Label
										htmlFor="apple-pay"
										className="font-medium cursor-pointer"
									>
										Apple Pay
									</Label>
								</div>
								<RadioGroupItem value="apple-pay" id="apple-pay" />
							</div>
						</RadioGroup>

						<Button className="w-full py-6 mt-4 bg-accent text-white rounded-xl">
							Proceed to Payment
						</Button>
					</CardContent>
				</Card>
			</div>
		</div>
	);
};

export default CreditCardPayment;
