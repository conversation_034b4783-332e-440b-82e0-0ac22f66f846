import { VideoCard } from "@/components/layout/learning/video-card";
import { SectionProps } from "@/features/learning/types";
import { SheetButton } from "@/components/layout/learning/resource-sheet";

export const MobileVideoSection = ({
	selectedSubject,
	setResourceType,
	resources,
}: SectionProps) => {
	if (resources.length === 0) return null;

	return (
		<div className="mt-4">
			<div className="px-4 flex justify-between items-center">
				<h3>Videos</h3>
				<SheetButton
					resources={resources}
					resourceType="video"
					selectedSubject={selectedSubject}
					setResourceType={setResourceType}
				/>
			</div>
			<div className="mt-2 flex gap-3 overflow-x-auto px-4 no-scrollbar">
				{resources.map((video) => (
					<div key={video.id} className="flex-shrink-0 w-72">
						<VideoCard resource={video} />
					</div>
				))}
			</div>
		</div>
	);
};
