import React from "react";

export interface LoadingCardProps {
	variant?: "analytics" | "info";
	className?: string;
}

const LoadingBlock = ({ className = "" }: { className?: string }) => (
	<div className={`animate-pulse bg-slate-100 rounded-md ${className}`}></div>
);

export const LoadingCard: React.FC<LoadingCardProps> = ({
	variant = "analytics",
	className = "",
}) => {
	if (variant === "analytics") {
		return (
			<div className={`rounded-[23px] p-6 bg-gray-50 ${className}`}>
				<div className="flex justify-between items-start">
					<div>
						<LoadingBlock className="h-4 w-24 mb-4" />
						<LoadingBlock className="h-8 w-12" />
					</div>
					<LoadingBlock className="w-[60px] h-[60px] rounded-[23px]" />
				</div>
				<div className="flex items-center mt-6">
					<LoadingBlock className="h-5 w-5 mr-2" />
					<LoadingBlock className="h-4 w-40" />
				</div>
			</div>
		);
	}

	return (
		<div
			className={`border border-[#CBD5E1] rounded-[23px] p-4 shadow-sm ${className}`}
		>
			<div className="flex flex-col gap-4">
				<LoadingBlock className="w-10 h-10 rounded-full" />
				<div>
					<LoadingBlock className="h-4 w-24 mb-2" />
					<LoadingBlock className="h-6 w-16" />
				</div>
			</div>
		</div>
	);
};

export default LoadingCard;
