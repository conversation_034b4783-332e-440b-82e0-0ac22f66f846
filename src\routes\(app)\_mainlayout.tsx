import Layout from "@/components/layout/main";
import Splash from "@/components/ui/splash";
import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";

export const Route = createFileRoute("/(app)/_mainlayout")({
	beforeLoad: ({ context, location }) => {
		// TEMPORARILY DISABLED FOR DEVELOPMENT - REMOVE IN PRODUCTION
		if (!context.auth.isLoggedIn) {
			throw redirect({
				to: "/account/login",
				search: {
					redirect: location.href,
				},
			});
		}
	},
	pendingComponent: Splash,
	component: () => (
		<div className="w-full min-h-screen">
			<Layout>
				<Outlet />
			</Layout>
		</div>
	),
});
