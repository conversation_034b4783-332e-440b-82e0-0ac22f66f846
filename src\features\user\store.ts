import { create } from "zustand";
import type {
	EducationalBackgroundValidation,
	EmptyValidation,
	OnboardUserValidation,
	PersonalInfoValidation,
} from "@/features/auth/schema";

interface UserStore {
	tempUserInfo: {
		[key: string]: string | string[];
	} | null;
	addInfoToTempUser: (
		user:
			| PersonalInfoValidation
			| EducationalBackgroundValidation
			| OnboardUserValidation
			| EmptyValidation
	) => void;
	clearTempUserInfo: () => void;
}

export const useUserStore = create<UserStore>()((set, get) => ({
	tempUserInfo: null,
	addInfoToTempUser: (
		user:
			| OnboardUserValidation
			| PersonalInfoValidation
			| EmptyValidation
			| EducationalBackgroundValidation
	) =>
		set({
			tempUserInfo: {
				...get().tempUserInfo,
				...Object.fromEntries(
					Object.entries(user).map(([key, value]) => [
						key,
						Array.isArray(value) || typeof value === "string"
							? value
							: String(value),
					])
				),
			},
		}),
	clearTempUserInfo: () => set({ tempUserInfo: null }),
}));
