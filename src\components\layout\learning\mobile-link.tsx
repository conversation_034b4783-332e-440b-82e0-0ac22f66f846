import { SectionProps } from "@/features/learning/types";
import { <PERSON><PERSON>Button } from "@/components/layout/learning/resource-sheet";
import { LinkCard } from "./link-card";

export const MobileLinkSection = ({
	selectedSubject,
	setResourceType,
	resources,
}: SectionProps) => {
	if (resources.length === 0) return null;

	return (
		<div className="mt-6 pb-6">
			<div className="px-4 flex justify-between items-center">
				<h3>Links</h3>
				<SheetButton
					resourceType="link"
					resources={resources}
					selectedSubject={selectedSubject}
					setResourceType={setResourceType}
				/>
			</div>
			<div className="mt-2 px-4 space-y-2">
				{resources.map((link) => (
					<LinkCard key={link.id} resource={link} />
				))}
			</div>
		</div>
	);
};
