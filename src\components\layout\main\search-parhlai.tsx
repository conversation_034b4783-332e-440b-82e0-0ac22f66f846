import { Input } from "@/components/ui/input";
import useMediaQuery from "@/hooks/use-media-query";
import { Search } from "react-feather";

interface SearchParhlaiProps {
	visibleInMobile?: boolean;
}

const SearchParhlai: React.FC<SearchParhlaiProps> = ({
	visibleInMobile = true,
}) => {
	const isDesktop = useMediaQuery("(min-width: 1024px)");
	if (isDesktop)
		return (
			<div className="flex max-w-96 flex-1 gap-x-3">
				<Input
					startIcon={Search}
					placeholder="Search Here"
					className="sans h-14 rounded-[10px] placeholder:text-gray-400 text-gray-600 items-center"
				/>
			</div>
		);
	else
		return (
			<div className={visibleInMobile ? "lg:hidden" : "invisible"}>
				<Search size={24} />
			</div>
		);
};

export default SearchParhlai;
