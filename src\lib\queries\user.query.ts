import { useMutation, useQuery } from "@tanstack/react-query";
import {
	getUser,
	onboardUser,
	updateUser,
	getProfilePictureUploadUrl,
	getProfilePictureUrl,
	getAnalytics,
} from "@/features/user/services";
import {
	OnboardUserValidation,
	UpdateUserValidation,
} from "@/features/auth/schema";

export const useGetUser = (userId: string) => {
	return useQuery({
		queryKey: ["user", userId],
		queryFn: () => getUser(userId),
		enabled: !!userId,
	});
};

export const useOnboardUser = () => {
	return useMutation({
		mutationFn: (data: OnboardUserValidation) => onboardUser(data),
	});
};

export const useUpdateUser = () => {
	return useMutation({
		mutationKey: ["updateUser"],
		mutationFn: (data: Partial<UpdateUserValidation>) => updateUser(data),
	});
};

export const useGetProfilePictureUploadUrl = () => {
	return useMutation({
		mutationKey: ["getProfilePictureUploadUrl"],
		mutationFn: () => getProfilePictureUploadUrl(),
	});
};

export const useGetProfilePictureUrl = (userId: string) => {
	return useQuery({
		queryKey: ["profilePictureUrl", userId],
		queryFn: () => getProfilePictureUrl(userId),
		enabled: false,
		refetchOnWindowFocus: false,
		staleTime: 1000 * 60 * 30, // 30 minutes
	});
};

export const useGetAnalytics = () => {
	return useQuery({
		queryKey: ["analytics"],
		queryFn: () => getAnalytics(),
		staleTime: 1000 * 60 * 5, // 5 minutes
		refetchOnWindowFocus: false,
	});
};

// Helper function to upload profile picture
export const uploadProfilePicture = async (file: File, uploadUrl: string) => {
	if (file.size > 1024 * 1024) {
		throw new Error("File size must be less than 1MB");
	}

	const validTypes = ["image/jpeg", "image/jpg", "image/png"];
	if (!validTypes.includes(file.type)) {
		throw new Error("File must be in JPG, JPEG or PNG format");
	}

	// Simple PUT request without modifying headers (which can invalidate presigned URL)
	const response = await fetch(uploadUrl, {
		method: "PUT",
		body: file,
		// Only set Content-Type, as this is typically allowed in the presigned URL
		headers: {
			"Content-Type": file.type,
		},
	});

	if (!response.ok) {
		throw new Error(
			`Failed to upload image: ${response.status} ${response.statusText}`
		);
	}

	return true;
};
