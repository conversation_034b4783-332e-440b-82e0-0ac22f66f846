export const MCQPlaceholder = () => (
	<div className="p-6 animate-pulse">
		<div className="flex items-center justify-between mb-4">
			<div className="h-5 bg-gray-200 rounded w-1/4"></div>
			<div className="flex gap-4">
				<div className="h-5 w-5 bg-gray-200 rounded"></div>
				<div className="h-5 w-5 bg-gray-200 rounded"></div>
			</div>
		</div>
		<div className="h-6 bg-gray-200 rounded mb-8 w-3/4"></div>
		<div className="space-y-6">
			<div className="h-4 bg-gray-200 rounded w-1/6 mb-6"></div>
			{[...Array(4)].map((_, i) => (
				<div key={i} className="h-5 bg-gray-200 rounded w-2/3"></div>
			))}
		</div>
	</div>
);
