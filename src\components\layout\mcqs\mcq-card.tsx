import { useState } from "react";
import {
	Bookmark,
	AlertTriangle,
	ChevronDown,
	ChevronUp,
	Watch,
} from "react-feather";
import { MCQ } from "@/features/mcqs/types";
import { Button } from "@/components/ui/button";

type MCQCardProps = {
	mcq: MCQ;
	selectedAnswer: number | undefined;
	isCorrect?: boolean; // Changed to optional since we're not using it directly
	liveCheckEnabled: boolean;
	showResults: boolean;
	questionNumber?: number;
};

const MCQCard = ({
	mcq,
	selectedAnswer,
	// isCorrect removed from destructuring since it's not used
	liveCheckEnabled,
	showResults,
	questionNumber,
}: MCQCardProps) => {
	const [showExpertAnswer, setShowExpertAnswer] = useState(false);
	const [isBookmarked, setIsBookmarked] = useState(false);
	const hasAnswered = selectedAnswer !== undefined;
	const showFeedback = (liveCheckEnabled || showResults) && hasAnswered;

	const toggleBookmark = () => {
		setIsBookmarked((prev) => !prev);
	};

	// Determine if the given answer is actually correct (regardless of isCorrect prop)
	const isActuallyCorrect = selectedAnswer === mcq.correctAnswer;

	return (
		<div className="p-6">
			<div className="flex items-center justify-between mb-4">
				<div className="flex items-center gap-2">
					<p className="text-gray-500 font-bold">
						QUESTION #{questionNumber !== undefined ? questionNumber : mcq.id}
					</p>
					{mcq.tag && (
						<span className="text-sm px-2 py-0.5 font-semibold border border-purple-300 bg-purple-100 text-accent rounded-full">
							{mcq.tag}
						</span>
					)}
				</div>
				<div className="flex items-center gap-4 mx-4">
					<Button
						variant="icon"
						className="[&_svg]:size-5 px-0"
						onClick={toggleBookmark}
					>
						<Bookmark
							className={`w-5 h-5 text-gray-600 ${isBookmarked ? "fill-accent" : ""}`}
						/>
					</Button>
					<AlertTriangle className="w-5 h-5 text-gray-600" />
				</div>
			</div>

			<h3 className="mb-8 text-xl">{mcq.question}</h3>

			<div className="space-y-2">
				<p className="font-bold text-gray-400 mb-6">CHOOSE ANSWER</p>
				{mcq.options.map((option, index) => (
					<div
						key={index}
						className={`p-1 ${showFeedback && index === mcq.correctAnswer ? "bg-green-50 rounded" : ""}`}
					>
						<span
							className={`text-gray-400 ${showFeedback && index === mcq.correctAnswer ? "font-bold" : ""}`}
						>
							({String.fromCharCode(65 + index)})
						</span>
						<span
							className={`text-base font-semibold ml-2 ${
								showFeedback && index === mcq.correctAnswer
									? "text-green-600"
									: showFeedback &&
										  index === selectedAnswer &&
										  !isActuallyCorrect
										? "text-red-600"
										: ""
							}`}
						>
							{option}
						</span>
						{showFeedback && index === mcq.correctAnswer && (
							<span className="ml-2 text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded">
								Correct Answer
							</span>
						)}
					</div>
				))}
			</div>

			{showFeedback && (
				<div className="mt-4">
					<div className="flex flex-col item-start sm:flex-row sm:items-center sm:justify-between gap-4">
						<div
							className={`py-2 px-4 rounded-full max-w-[90%] ${
								isActuallyCorrect
									? "bg-green-100 text-green-700"
									: "bg-red-100 text-red-700"
							} flex items-center gap-2`}
						>
							<span className="text-sm flex items-center gap-2">
								<Watch className="w-4 h-4" />
								{isActuallyCorrect
									? "You have chosen the correct answer."
									: "You have chosen the wrong answer."}
							</span>
						</div>
						<Button
							variant="link"
							onClick={() => setShowExpertAnswer(!showExpertAnswer)}
							className="justify-start text-gray-600 flex items-center gap-1 text-sm"
						>
							{showExpertAnswer ? "Hide" : "View"} expert answer
							{showExpertAnswer ? (
								<ChevronUp size={16} />
							) : (
								<ChevronDown size={16} />
							)}
						</Button>
					</div>

					{showExpertAnswer && (
						<div className="mt-2 p-4 bg-gray-50 rounded-md">
							<p className="text-sm">{mcq.description}</p>
							{!isActuallyCorrect && (
								<div className="mt-2 pt-2 border-t border-gray-200">
									<p className="text-sm font-medium text-green-600">
										Correct answer:{" "}
										{String.fromCharCode(65 + mcq.correctAnswer)} -{" "}
										{mcq.options[mcq.correctAnswer]}
									</p>
								</div>
							)}
						</div>
					)}
				</div>
			)}
		</div>
	);
};

export default MCQCard;
