import React from 'react';
import { <PERSON><PERSON><PERSON> as Recha<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';
import { <PERSON><PERSON>ontainer, ChartTooltip, ChartTooltipContent, type ChartConfig } from '@/components/ui/chart';

export interface DonutChartData {
  name: string;
  value: number;
  fill: string;
  total?: string;
}

export interface DonutChartProps {
  data: DonutChartData[];
  title?: string;
  centerText?: string;
  centerSubtext?: string;
  showTooltip?: boolean;
  showLegend?: boolean;
  innerRadius?: number;
  outerRadius?: number;
  className?: string;
  config?: ChartConfig;
}

const defaultConfig: ChartConfig = {
  value: {
    label: "Value",
  },
};

export const DonutChart: React.FC<DonutChartProps> = ({
  data,
  title,
  centerText,
  centerSubtext,
  showTooltip = true,
  showLegend = true,
  innerRadius = 60,
  outerRadius = 80,
  className = "",
  config = defaultConfig,
}) => {
  console.log('DonutChart rendering:', { data, className, innerRadius, outerRadius });
  // Create chart config from data
  const chartConfig: ChartConfig = {
    ...config,
    ...data.reduce((acc, item) => {
      acc[item.name.toLowerCase().replace(/\s+/g, '_')] = {
        label: item.name,
        color: item.fill,
      };
      return acc;
    }, {} as ChartConfig),
  };

  // For single value charts, create data with background
  const chartData = data.length === 1 && data[0]
    ? [
        { name: data[0].name, value: data[0].value, fill: data[0].fill },
        { name: 'Background', value: 100 - data[0].value, fill: '#E5E7EB' }
      ]
    : data;

  // Check if this is a small chart and extract dimensions
  const isSmallChart = className.includes('w-[40px]') || className.includes('w-[50px]') || className.includes('w-[63px]');

  // Extract width from className for small charts
  const getChartSize = () => {
    if (className.includes('w-[63px]')) return 63;
    if (className.includes('w-[50px]')) return 50;
    if (className.includes('w-[40px]')) return 40;
    return 63; // default
  };

  const chartSize = getChartSize();

  // Use Shadcn charts for small donut charts
  if (isSmallChart) {
    console.log('Rendering small chart:', { data, chartSize, className });

    // For small charts, use the data as-is (can handle multiple segments)
    const smallChartData = data;

    // Create config for small chart
    const smallChartConfig: ChartConfig = {
      ...data.reduce((acc, item) => {
        acc[item.name.toLowerCase().replace(/\s+/g, '_')] = {
          label: item.name,
          color: item.fill,
        };
        return acc;
      }, {} as ChartConfig),
    };

    return (
      <div className={className}>
        <ChartContainer
          config={smallChartConfig}
          className="w-full h-full aspect-square"
        >
          <RechartsPieChart width={chartSize} height={chartSize}>
            <Pie
              data={smallChartData}
              cx="50%"
              cy="50%"
              innerRadius={innerRadius}
              outerRadius={outerRadius}
              dataKey="value"
              startAngle={90}
              endAngle={450}
              strokeWidth={1}
							stroke="#ffffff"
            >
              {smallChartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
            </Pie>
            {showTooltip && (
              <ChartTooltip
                cursor={false}
                content={<ChartTooltipContent
                  hideLabel
                  formatter={(value, name) => {
                    return [`${value} `, name];
                  }}
                />}
              />
            )}
          </RechartsPieChart>
        </ChartContainer>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      {title && (
        <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">
          {title}
        </h3>
      )}
      <div className="relative">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[200px]"
        >
          <RechartsPieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              innerRadius={innerRadius}
              outerRadius={outerRadius}
              dataKey="value"
              startAngle={90}
              endAngle={450}
              strokeWidth={1}
							stroke="#ffffff"
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
            </Pie>
            {showTooltip && (
              <ChartTooltip
                cursor={false}
                content={<ChartTooltipContent
                  hideLabel
                  formatter={(value, name) => {
                    // Don't show tooltip for background segments
                    if (name === 'Background') return null;
                    return [`${value} `, name];
                  }}
                />}
              />
            )}
          </RechartsPieChart>
        </ChartContainer>
        
        {/* Center text overlay */}
        {(centerText || centerSubtext) && (
          <div className="absolute inset-0 flex flex-col items-center justify-center pointer-events-none">
            {centerText && (
              <div className="text-2xl font-bold text-gray-800">
                {centerText}
              </div>
            )}
            {centerSubtext && (
              <div className="text-sm text-gray-500 mt-1">
                {centerSubtext}
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Legend below the chart */}
      {showLegend && (
        <div className="flex flex-wrap justify-center gap-4 mt-4">
          {data.map((item, index) => (
            <div key={index} className="flex items-center gap-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: item.fill }}
              />
              <span className="text-sm text-gray-600">
                {item.name}
              </span>
              <span className="text-sm font-semibold text-gray-800">
                {item.value} {item.total && `total`}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
