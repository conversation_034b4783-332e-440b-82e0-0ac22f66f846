import React from "react";
import { <PERSON><PERSON><PERSON> as <PERSON>cha<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "recharts";
import {
	<PERSON><PERSON><PERSON><PERSON>,
	ChartTooltip,
	ChartTooltipContent,
	type ChartConfig,
} from "@/components/ui/chart";

export interface PieChartData {
	name: string;
	value: number;
	fill: string;
}

export interface PieChartProps {
	data: PieChartData[];
	title?: string;
	showLegend?: boolean;
	showTooltip?: boolean;
	innerRadius?: number;
	outerRadius?: number;
	className?: string;
	config?: ChartConfig;
}

const defaultConfig: ChartConfig = {
	value: {
		label: "Value",
	},
};

export const PieChart: React.FC<PieChartProps> = ({
	data,
	title,
	showLegend = true,
	showTooltip = true,
	innerRadius = 0,
	outerRadius = 80,
	className = "",
	config = defaultConfig,
}) => {
	// Create chart config from data
	const chartConfig: ChartConfig = {
		...config,
		...data.reduce((acc, item) => {
			acc[item.name.toLowerCase().replace(/\s+/g, "_")] = {
				label: item.name,
				color: item.fill,
			};
			return acc;
		}, {} as ChartConfig),
	};

	// Calculate total for percentage calculation
	const total = data.reduce((sum, item) => sum + item.value, 0);

	return (
		<div className={`w-full ${className}`}>
			{title && (
				<h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">
					{title}
				</h3>
			)}
			<div className="w-full h-[200px] mb-4">
				<ChartContainer config={chartConfig} className="w-full h-full">
					<RechartsPieChart>
						<Pie
							data={data}
							cx="50%"
							cy="50%"
							innerRadius={innerRadius}
							outerRadius={outerRadius}
							dataKey="value"
							strokeWidth={2}
							stroke="#ffffff"
						>
							{data.map((entry, index) => (
								<Cell key={`cell-${index}`} fill={entry.fill} />
							))}
						</Pie>
						{showTooltip && (
							<ChartTooltip
								cursor={false}
								content={
									<ChartTooltipContent
										hideLabel
										formatter={(value, name) => [`${value} `, name]}
										labelFormatter={() => ""}
									/>
								}
							/>
						)}
					</RechartsPieChart>
				</ChartContainer>
			</div>

			{/* Custom Legend */}
			{showLegend && (
				<div className="flex flex-col gap-2">
					{data.map((item, index) => (
						<div key={index} className="flex items-center gap-3">
							<div
								className="w-4 h-4 rounded-full flex-shrink-0"
								style={{ backgroundColor: item.fill }}
							/>
							<span className="text-sm font-medium text-gray-600 flex-1">
								{item.name}
							</span>
							<span className="text-sm font-medium text-black">
								{Math.round((item.value / total) * 100)}%
							</span>
						</div>
					))}
				</div>
			)}
		</div>
	);
};
