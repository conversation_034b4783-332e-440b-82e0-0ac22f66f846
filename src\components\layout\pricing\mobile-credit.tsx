import { useState } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { ArrowLeft } from "react-feather";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { ICONS } from "@/lib/assets/images";

type MobileCreditCardProps = {
	price: number;
	period: string;
	onBack: () => void;
	onProceed?: () => void;
};

const MobileCreditCard = ({
	price,
	period,
	onBack,
	onProceed = () => {},
}: MobileCreditCardProps) => {
	const [selectedPaymentMethod, setSelectedPaymentMethod] =
		useState("credit-card");

	return (
		<div className="min-h-screen bg-gradient-to-r from-[#CBBBFF] via-[#CBBBFF20] to-[#FFFFFF00] flex flex-col items-center p-4 space-y-16">
			<div className="w-full flex items-center justify-between mt-10">
				<Button
					variant="ghost"
					size="sm"
					onClick={onBack}
					className="p-1 h-8 w-8"
				>
					<ArrowLeft className="h-5 w-5 text-gray-700" />
				</Button>

				<div className="flex-grow flex justify-center">
					<img src={ICONS.logoexpanded} alt="parhali" className="w-24" />
				</div>

				<div className="w-8"></div>
			</div>

			<Card className="w-full max-w-md bg-white shadow-md rounded-xl">
				<CardHeader className="text-center pt-6 pb-2">
					<h1 className="text-2xl font-bold">Payment</h1>
					<p className="text-xs text-gray-600 mt-1">
						Please choose a payment method.
					</p>
				</CardHeader>

				<CardContent className="p-6 space-y-5">
					<div className="text-center mb-2">
						<span className="text-sm font-bold">Rs.</span>
						<span className="text-2xl font-bold">{price}</span>
						<span className="text-xs text-gray-500">
							{" "}
							for {period.toLowerCase()}
						</span>
					</div>

					<div>
						<p className="text-xs font-medium text-gray-700 mb-3">
							Please select payment method:
						</p>

						<RadioGroup
							value={selectedPaymentMethod}
							onValueChange={setSelectedPaymentMethod}
							className="space-y-3"
						>
							<div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
								<div className="flex items-center gap-3">
									<img
										src={ICONS.creditcard}
										alt="Mastercard"
										className="h-8 w-8 rounded-md"
									/>
									<Label htmlFor="credit-card" className="text-sm font-medium">
										Credit card
									</Label>
								</div>
								<RadioGroupItem value="credit-card" id="credit-card" />
							</div>

							<div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
								<div className="flex items-center gap-3">
									<img
										src={ICONS.paypal}
										alt="PayPal"
										className="h-8 w-8 rounded-md"
									/>
									<Label htmlFor="paypal" className="text-sm font-medium">
										Paypal
									</Label>
								</div>
								<RadioGroupItem value="paypal" id="paypal" />
							</div>

							<div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
								<div className="flex items-center gap-3">
									<img
										src={ICONS.applepay}
										alt="Apple Pay"
										className="h-8 w-8 rounded-md"
									/>
									<Label htmlFor="apple-pay" className="text-sm font-medium">
										Apple Pay
									</Label>
								</div>
								<RadioGroupItem value="apple-pay" id="apple-pay" />
							</div>
						</RadioGroup>
					</div>

					{/* Proceed Button */}
					<Button
						onClick={onProceed}
						className="w-full py-3 mt-4 bg-accent text-white rounded-lg"
					>
						Proceed to Payment
					</Button>
				</CardContent>
			</Card>
		</div>
	);
};

export default MobileCreditCard;
