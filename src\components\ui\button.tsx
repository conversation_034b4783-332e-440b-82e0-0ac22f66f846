import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";
import Spinner from "./spinner";

const buttonVariants = cva(
	"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-[10px] text-sm font-medium transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:brightness-90 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
	{
		variants: {
			variant: {
				default: "bg-primary text-primary-foreground hover:brightness-90",
				destructive:
					"bg-destructive text-destructive-foreground hover:bg-destructive/90",
				outline: "border border-input bg-background hover:bg-slate-100 ",
				secondary:
					"bg-secondary text-secondary-foreground hover:bg-secondary/80",
				ghost: "hover:bg-accent hover:text-accent-foreground",
				link: "text-primary underline-offset-4 font-bold  hover:underline",
				active: "lg:bg-[#e6e1f8] text-primary lg:text-label",
				inactive:
					"lg:hover:bg-slate-100 lg:hover:text-slate-900 lg:text-icongray text-gray-600",
				icon: "bg-transparent text-gray-900 hover:bg-transparent",
			},
			size: {
				default: "h-10 px-4 py-2",
				sm: "h-9 rounded-md px-3",
				lg: "h-11 rounded-md px-8",
				icon: "h-10 w-10",
			},
		},
		defaultVariants: {
			variant: "default",
			size: "default",
		},
	}
);

export interface ButtonProps
	extends React.ButtonHTMLAttributes<HTMLButtonElement>,
		VariantProps<typeof buttonVariants> {
	asChild?: boolean;
	loading?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
	(
		{ className, variant, size, asChild = false, loading = false, disabled = false, ...props },
		ref
	) => {
		const Comp = asChild ? Slot : "button";
		return (
			<Comp
				className={cn(buttonVariants({ variant, size, className }), {
					"opacity-50": loading,
					"opacity-60 cursor-not-allowed": disabled,
				})}
				ref={ref}
				disabled={loading || disabled}
				{...props}
			>
				{loading ? <Spinner /> : props.children}
			</Comp>
		);
	}
);
Button.displayName = "Button";

export { Button, buttonVariants };
