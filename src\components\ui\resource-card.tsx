import { ArrowRight } from "react-feather";
import { Button } from "@/components/ui/button";
import { IMAGES } from "@/lib/assets/images";
import { Link } from "@tanstack/react-router";

interface ResourceCardProps {
	title: string;
	description: string;
	buttonText: string;
	buttonLink: string;
}

export function ResourceCard({
	title,
	description,
	buttonText,
	buttonLink,
}: ResourceCardProps) {
	return (
		<div className="rounded-3xl bg-accent py-4 px-12 text-white">
			<div className="flex flex-col md:flex-row md:items-center md:justify-between">
				<div className="flex flex-col justify-between md:mr-8">
					<div className="space-y-8 lg:space-y-12 lg:w-5/6 ">
						<div>
							<h1 className="font-bold">{title}</h1>
							<p className="text-xl font-medium text-purple-100">
								{description}
							</p>
						</div>
						<div className="pt-2">
							<Button
								variant="secondary"
								className="bg-white p-6 text-md hover:bg-purple-100"
								asChild
							>
								<Link to={buttonLink}>
									{buttonText} <ArrowRight className="ml-2 h-4 w-4" />{" "}
								</Link>
							</Button>
						</div>
					</div>
				</div>
				<div className="hidden md:block ">
					<img
						src={IMAGES.illustration}
						height={393}
						width={393}
						alt="Resources illustration"
						className="h-auto w-full"
					/>
				</div>
			</div>
		</div>
	);
}
