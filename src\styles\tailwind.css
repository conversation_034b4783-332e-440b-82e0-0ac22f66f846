@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
	:root {
		--background: 210, 40%, 98%;
		--foreground: 222.2 84% 4.9%;
		--card: 0 0% 100%;
		--card-foreground: 222.2 84% 4.9%;
		--popover: 0 0% 100%;
		--popover-foreground: 222.2 84% 4.9%;
		--primary: 254, 60%, 51%;
		--primary-foreground: 0, 0%, 100%;
		--secondary: 210 40% 96.1%;
		--secondary-foreground: 222.2 47.4% 11.2%;
		--tertiary: 180, 34%, 48%;
		--tertiary-foreground: 0, 0%, 100%;
		--muted: 210 40% 96.1%;
		--muted-foreground: 215.4 16.3% 46.9%;
		--accent: 254, 60%, 51%;
		--accent-foreground: 222.2 47.4% 11.2%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 210 40% 98%;
		--border: 214.3 31.8% 91.4%;
		--input: 214.3 31.8% 91.4%;
		--ring: 222.2 84% 4.9%;
		--chart-1: 12 76% 61%;
		--chart-2: 173 58% 39%;
		--chart-3: 197 37% 24%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
		--radius: 0.5rem;
	}
	.dark {
		--background: 222.2 84% 4.9%;
		--foreground: 210 40% 98%;
		--card: 222.2 84% 4.9%;
		--card-foreground: 210 40% 98%;
		--popover: 222.2 84% 4.9%;
		--popover-foreground: 210 40% 98%;
		--primary: 210 40% 98%;
		--primary-foreground: 222.2 47.4% 11.2%;
		--secondary: 217.2 32.6% 17.5%;
		--secondary-foreground: 210 40% 98%;
		--muted: 217.2 32.6% 17.5%;
		--muted-foreground: 215 20.2% 65.1%;
		--accent: 217.2 32.6% 17.5%;
		--accent-foreground: 210 40% 98%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 210 40% 98%;
		--border: 217.2 32.6% 17.5%;
		--input: 217.2 32.6% 17.5%;
		--ring: 212.7 26.8% 83.9%;
		--chart-1: 254 60% 51%;
		--chart-2: 254 60% 71%;
		--chart-3: 254 60% 81%;
		--chart-4: 254 60% 91%;
		--chart-5: 254 60% 96%;
	}
}
@layer base {
	* {
		@apply border-border;
	}
	body {
		font-family: "Inter", sans-serif;
		@apply bg-background text-foreground;
	}

	h1 {
		@apply text-xl sm:text-2xl md:text-3xl lg:text-4xl font-extrabold;
	}

	h2 {
		@apply text-lg sm:text-xl md:text-2xl lg:text-3xl font-semibold;
	}

	h3 {
		@apply text-base sm:text-lg md:text-xl lg:text-2xl font-semibold;
	}

	h4 {
		@apply text-xl sm:text-2xl md:text-3xl lg:text-4xl;
	}
	/* 
	h5 {
		@apply text-lg sm:text-xl md:text-2xl lg:text-3xl;
	}

	h6 {
		@apply text-base sm:text-lg md:text-xl lg:text-2xl;
	} */

	p {
		@apply text-sm md:text-base;
	}
}
/* 
	---break--- */
@layer base {
  * {
    @apply border-border outline-ring/50;
	}
  body {
    @apply bg-background text-foreground;
	}
}

@layer utilities {
  .scrollbar-hide {
    /* Hide scrollbar for Chrome, Safari and Opera */
    &::-webkit-scrollbar {
      display: none;
    }
    /* Hide scrollbar for IE, Edge and Firefox */
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
}
