import { <PERSON><PERSON> } from "@/components/ui/button";
import { IMAGES } from "@/lib/assets/images";
import { ChevronRight } from "react-feather";

const UpgradePlan = () => {
	return (
		<div className="rounded-[19px] relative items-center lg:bg-[#e6e1f8] lg:p-5 px-2 lg:pt-40 flex lg:flex-col">
			<img
				src={IMAGES.prolock}
				alt="pro lock"
				className="lg:absolute -top-12 size-24 lg:size-[215px]"
			/>
			<p className="lg:text-center text-white lg:text-foreground px-2">
				Upgrade to <span className="lg:text-primary">PRO</span> for more
				features.
			</p>
			<Button className="w-full h-12 hidden lg:block">Upgrade Plan</Button>
			<ChevronRight size={24} className="text-white lg:hidden" />
		</div>
	);
};

export default UpgradePlan;
